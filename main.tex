%\documentclass[conference]{IEEEtran}
\documentclass[lettersize,journal]{IEEEtran}
%\documentclass[10pt, conference, letterpaper]{IEEEtran}

\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algpseudocode}

\usepackage{algorithm}
\usepackage{algorithmicx}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{enumitem}
\usepackage{subcaption}

\newtheorem{remark}{Remark}


\begin{document}

\title{Dynamic ON-OFF Control with Trajectory prediction for Multi-RIS Wireless Networks through Generative Model\\

%\thanks{Identify applicable funding agency here. If none, delete this.}
}



\maketitle

\begin{abstract}
Reconfigurable intelligent surfaces (RISs) have demonstrated an unparalleled ability to reconfigure wireless environments by dynamically controlling the phase, amplitude, and polarization of impinging waves. However, as nearly passive reflective metasurfaces, RISs may not distinguish between desired and interference signals, which can lead to severe spectrum pollution and even affect performance negatively. In particular, in large-scale networks, the signal-to-interference-plus-noise ratio (SINR) at the receiving node can be degraded due to excessive interference reflected from the RIS. To overcome this fundamental limitation, we propose in this paper a trajectory prediction-based dynamical control algorithm (TPC) for anticipating RIS ON-OFF states sequence, integrating a long-short-term-memory (LSTM) scheme to predict user trajectories. In particular, through a codebook-based algorithm, the RIS controller adaptively coordinates the configuration of the RIS elements to maximize the received SINR. Our simulation results demonstrate the superiority of the proposed TPC method over various system settings.
\end{abstract}

\begin{IEEEkeywords}
Reconfigurable intelligent surface, ON-OFF control, trajectory prediction, interference mitigation
\end{IEEEkeywords}

\section{Introduction}
With the booming development of global sports events, marathons have emerged as one of the most influential outdoor competitions due to their unique challenges and broad public participation. However, the high-intensity and long-duration nature of marathons subjects athletes to significant physiological stress during prolonged running\cite{tokyoMed2022}. Common issues such as elevated core body temperature \cite{Casa2015}, excessive heart rate and blood pressure fluctuations \cite{cardiac2009，Armstrong1997}, oxygen desaturation, and hyponatremia \cite{Barnes2019} frequently occur. Proper hydration management is crucial for athlete safety, as human water needs vary significantly during endurance activities \cite{Sawka2005}. Subtle changes in these vital signs often serve as critical early warnings for serious emergencies such as heatstroke, syncope, or even sudden cardiac arrest. According to statistics from World Athletics, the incidence of emergency medical events caused by sudden health problems during marathons has shown an upward trend in recent years. Therefore, establishing an efficient and stable system for real-time monitoring and emergency response of athletes' vital signs has become a key challenge in the development of intelligent medical support systems.

In recent years, wearable technologies have made significant progress and achieved wide adoption. Athletes can now use smart wristbands, patches, and other wearable devices to regularly upload key physiological data such as body temperature, heart rate, and blood oxygen saturation. This provides event medical centers with real-time and dynamic monitoring capabilities, significantly enhancing the timeliness and accuracy of medical support. However, the reliability of this process heavily depends on the stability and low latency of wireless communication links. Marathon routes often stretch for dozens of kilometers and traverse complex and dynamic environments, with rapidly changing population density. Traditional base station deployment strategies struggle to provide seamless full-route coverage, and a high density of co-channel users can easily lead to severe signal interference. Once the communication link fails, critical vital sign data may not be successfully uploaded, resulting in missed optimal rescue opportunities and posing a serious threat to athletes’ safety.

To address the challenges of communication blind zones and unstable links, the sixth-generation (6G) communication systems introduce Reconfigurable Intelligent Surface (RIS) technology \cite{risRoadmap, 10596064}. RIS consists of a large number of low-power, low-cost passive reflecting elements, which can precisely control the phase and amplitude of the reflected signals at the physical layer to “reconstruct” the wireless channel. This unique capability enables RIS to enhance signal strength, expand coverage areas, and improve energy efficiency. RIS has been widely applied in integrated space-air-ground communications \cite{yangjsac}, intelligent transportation \cite{TITS}, underwater communications \cite{aRIS}, and other key scenarios, becoming a vital enabling technology for future intelligent communications. In marathon scenarios, deploying multiple RIS panels along the race route can theoretically enhance the quality of wireless signals around athletes, significantly improving the success rate of real-time data transmission and offering strong support for athlete safety.
 Recent advances in RIS-assisted large-scale wireless networks have demonstrated significant performance improvements through stochastic geometry analysis \cite{wangtianxiong}, while intelligent reflecting surface architectures continue to evolve with considerations for both distributed and centralized deployment strategies \cite{wuTWC}. Additionally, high-mobility communication scenarios benefit from intelligent refracting surfaces that can transform fading channels from fast to slow \cite{zhangTWC}.

However, the passive and blind reflection characteristic of RIS also introduces new challenges. Since RIS cannot distinguish the source of incoming signals, it may amplify interference signals from nearby users or spectators while reflecting the desired signal. When multiple users are within the overlapping reflection path of an RIS, problems such as spectral pollution, interference amplification, and decreased signal-to-interference-plus-noise ratio (SINR) can easily arise \cite{nointerference1,9864655,10729719}. Comprehensive interference analysis in RIS-assisted MIMO systems has revealed the complexity of these challenges \cite{nointerference2}, while realistic reradiation models further highlight the effects of practical implementation constraints \cite{noI1}. Recent system-level simulations for industry standards have provided valuable insights into interference mitigation strategies \cite{noI2}. As illustrated in Fig.~\ref{fig:modelDesign}, in extreme cases where an interfering user is closely aligned with the target user, conventional RIS reflection not only fails to improve communication quality but can also exacerbate communication conflicts, severely compromising data accuracy and link stability.

To address these challenges, both academia and industry have proposed various environment-aware RIS control methods. These include phase adjustment strategies based on channel characteristics, interference-aware control using intelligent spectrum learning \cite{tvt}, reinforcement learning-based scheduling \cite{icc}, and energy-rate trade-off methods via component-level switching control \cite{tcomm}. Advanced approaches leverage multi-branch attention convolutional neural networks for online RIS configuration \cite{********}, while statistical CSI-based beamforming techniques have been developed for RIS-aided multiuser MISO systems using deep reinforcement learning \cite{CSIris}. Although these approaches have shown promise, they generally assume static or quasi-static channels and do not account for the influence of user mobility in highly dynamic scenarios such as marathons. This leads to limited practical effectiveness and restricts their application value.

To solve the above issues, this paper proposes a trajectory prediction-based dynamic RIS switch control strategy, referred to as Trajectory Prediction Control (TPC). A variety of trajectory prediction models have been developed to address different scenarios: Trajectory prediction models, such as Social LSTM, help forecast human movement in crowded spaces \cite{Alahi2016}; Generative approaches like Social GAN are widely used for socially aware trajectory forecasting \cite{Gupta2018}; Transformer-based networks are also gaining traction in forecasting pedestrian trajectories \cite{Giuliari2020}; Some models work with momentary observations to predict future human movement \cite{Sun2022}; Dynamically feasible trajectory models like Trajectron++ use heterogeneous data for higher accuracy \cite{Salzmann2020}; Multi-modal distributions for pedestrian paths are captured effectively using GANs \cite{Amirian2019}; Recent deep learning-based reviews highlight the advances in pedestrian trajectory forecasting \cite{Kothari2021}; Long-term movement modeling from GPS data offers valuable insights for predictive path analysis \cite{Sun2020}. In this paper, the approach employs a Long Short-Term Memory (LSTM) network \cite{lstm} to accurately predict athletes’ future trajectories. Leveraging its unique gating mechanism, the LSTM model can effectively handle long-term dependencies in time-series data and capture dynamic movement patterns. By combining predictions of interfering users, the base station can proactively determine RIS switching states and phase configurations to maximize the SINR of target users. This strategy enables proactive interference avoidance and channel quality assurance several time slots in advance. As shown on the right of Fig.~\ref{fig:modelDesign}, the system dynamically adjusts RIS control states based on spatial movement trends, significantly improving communication reliability and response time in key areas and ensuring stable transmission of vital sign data.

In practice, inevitable trajectory prediction errors may lead to RIS configuration deviations, affecting data transmission quality and athlete safety. To mitigate this issue, the proposed approach further optimizes the prediction model by incorporating additional features—namely, user speed and movement angle at each time step. These indicators provide a more comprehensive description of motion states, and their integration into the prediction model enhances trajectory prediction accuracy, thereby improving SINR stability and offering more reliable protection for athletes.

Furthermore, considering that RIS control involves a complex mix of discrete and continuous variables in a high-dimensional and non-convex space, traditional reinforcement learning or heuristic optimization methods often struggle with local optima and low sample efficiency. To overcome these limitations, this paper innovatively introduces Diffusion Models as RIS optimizers. Diffusion probabilistic models have opened new avenues in generative modeling \cite{Ho2020}; Improvements on denoising diffusion models offer better performance for generative tasks \cite{Nichol2021}; Score-based generative modeling via SDEs enhances trajectory and signal prediction capabilities \cite{Song2021}; Latent diffusion models have enabled high-resolution generation in complex tasks \cite{Rombach2022}; Comprehensive surveys show the impact of diffusion models across vision domains \cite{Croitoru2023}; Uplink channel estimation has also benefited from diffusion model integration in RIS systems \cite{Wang2025}; Recent works integrate decision transformers and diffusion models for RIS-assisted tasks \cite{DecisionDT2025}. Conditioned on trajectory and channel state information, the diffusion model formulates a reverse denoising generation process to perform end-to-end optimization of RIS switch vectors and phase configurations. The proposed diffusion-based optimization framework significantly enhances the robustness of the system under highly dynamic interference, improves the feasibility and flexibility of RIS control, and offers a novel paradigm and methodology for RIS optimization in complex wireless communication environments.

In summary, this paper presents an intelligent RIS control framework that integrates trajectory prediction and diffusion optimization, tailored for athlete safety assurance in marathon scenarios. The method balances user mobility, RIS optimization, and communication stability, providing a new paradigm for intelligent wireless networks under the 6G vision that integrates sensing, computing, and control. Extensive simulation experiments validate the framework’s superior performance in enhancing communication reliability and ensuring athlete safety, making it a promising theoretical and technical reference for smart communication and safety systems in marathons and other high-mobility scenarios.

\begin{figure*}
    \centering
    \begin{subfigure}{0.45\textwidth}
        \includegraphics[width=\textwidth]{modelDesign_pre.png}
        \caption{Without TPC, RIS reflects both desired and interfering signals, leading to potential performance degradation.}
        \label{fig:modelDesign}
    \end{subfigure}
    \begin{subfigure}{0.45\textwidth}
        \includegraphics[width=\textwidth]{modelDesign_aft.png}
        \caption{With TPC, the BS predicts runner trajectories and dynamically controls RIS ON/OFF states, enhancing signal quality and reducing interference in fast-changing marathon environments.}
        \label{fig:modelDesign}
    \end{subfigure}
\end{figure*}

\begin{figure*}
    \centering

\end{figure*}

\section{System Model and Problem Formulation} \label{s2}
%In this section, we introduce the system models, channel models, and problem formulations that we use in RIS configuration.
\subsection{RISs-aided Network Scenario}
We consider an RIS-assisted uplink communication system, which consists of a base station (BS), a set of reconfigurable intelligent surfaces (RISs), and a set of $U$ mobile users, where typically $R \geq 1$ and $U \geq R$ holds. Let the set of RISs be denoted as $\mathcal{R}$ and the set of users as $\mathcal{U}$, where each RIS is indexed as $\mathrm{RIS}_i, i \in \mathcal{R}$, and each user as $U_u, u \in \mathcal{U}$.

As illustrated in Fig.~\ref{fig:modelDesign}, we divide all users into two disjoint subsets: $\mathcal{U}_c$ and $\mathcal{U}_j$. The users in $\mathcal{U}_c$ are the target users who upload critical physiological data (e.g., real-time heart rate, body temperature, oxygen saturation, etc.), while $\mathcal{U}_j$ includes the remaining users who might be uploading other types of data in the same area or carrying smart devices, leading to potential signal interference when competing for uplink resources. Clearly, we have $\mathcal{U}_c + \mathcal{U}_j = \mathcal{U}$.

We assume that each RIS serves only one user. Each RIS has two states: ON or OFF. We introduce a binary control variable $v_i \in \{0,1\}$ to represent the working state of RIS$_i$. When $v_i=1$, RIS$_i$ is ON and reflects the signal; when $v_i=0$, RIS$_i$ is OFF and does not participate in signal reflection.

Moreover, we assume that each RIS consists of $N$ reflecting elements, and each element supports continuous phase tuning. All RISs are centrally controlled by the BS, which dynamically adjusts their ON/OFF status via control signaling to adaptively match the transmission environment. Particularly, in our proposed method, a trajectory prediction mechanism is employed to estimate users’ future locations and interference distribution. Based on this, the BS can configure RIS ON/OFF states in advance to improve SINR during vital sign data upload, thereby ensuring stable transmission and medical monitoring.

\subsection{Channel Model}
We consider the product-distance PL model to characterize the path loss in the uplink communication scenario. Let the transmit power be $P$ and small-scale fading be $g$. Since marathons are typically held in urban or suburban areas with complex environments and large crowds, we assume that the direct link between the BS and the desired user U$_u$ is subject to Rayleigh fading. The path loss of the direct link is expressed as
\begin{equation}
    \eta_u = C d_u^{-\alpha},
\end{equation}
where $C = \left( \frac{c \sqrt{G_t G_r}}{4\pi f} \right)^2$ is the unit-distance free space path loss constant, including the speed of light $c$, carrier frequency $f$, transmit and receive antenna gains $G_t$ and $G_r$, respectively. $d_u$ is the distance between user U$_u$ and the BS, and $\alpha$ is the path loss exponent.

Given that the RISs are deployed on high structures such as lamp posts or building facades along the marathon route, we assume that the horizontal distance dominates and the height differences between the BS, RISs, and users are negligible.

The path loss of the reflected link through RIS (i.e., U$_u \rightarrow$ RIS$_i \rightarrow$ BS) is modeled as
\begin{equation}
    \eta_{iu} = C(d_i d_{ui})^{-\alpha},
\end{equation}
where $d_i$ is the distance from RIS$_i$ to the BS, and $d_{ui}$ is the distance from user U$_u$ to RIS$_i$. $C$ is the same unit-distance PL constant.

For small-scale fading, we denote $h_i$ as the channel gain from RIS$_i$ to BS, and $h_{ui}$ as the channel gain from user U$_u$ to RIS$_i$. These are normalized small-scale fading coefficients.

The signal received by the BS from the desired user U$_l$ is given by
\begin{equation}
    y_l = \left( \sqrt{\eta_l} g_l + \sum_{i=1}^{R} \sqrt{\eta_{li}} h_i \Phi_l h_{li} \right) \sqrt{P} s_l + I + \sigma,
\end{equation}
where $\Phi_l$ is the reflection matrix of RIS$_i$ for U$_l$, $s_l$ is the life signal transmitted by U$_l$, and $\sigma$ is the additive white Gaussian noise (AWGN). The interference term $I$ caused by other users is expressed as
\begin{equation}
    I = \sqrt{P} \left( \sum_{m=1}^{U_I} \sqrt{\eta_m} g_m + \sum_{i=1}^{R} \sqrt{\eta_{mi}} v_i h_i \Phi_m h_{mi} \right),
\end{equation}
where $U_I$ denotes the number of interfering users and $v_i$ is the ON/OFF state of RIS$_i$.

It is worth noting that in large-scale marathon scenarios, interference sources may include nearby or co-located users, onlookers, or smart wearable devices. These interferences severely degrade the uplink quality of critical physiological data from target users. Therefore, intelligent RIS control and user trajectory prediction are essential to dynamically adjust the RIS states for interference mitigation.


\begin{figure*}
    \centering
    \includegraphics[width=1.24\columnwidth]{algorithm8.png}
    \caption{The proposed TPC algorithm structure. The input includes the history points of the pedestrian trajectories, and the output is the predicted coordinates.}
    \label{fig:algorithm}
\end{figure*}




\subsection{Problem Formulation and Analysis}
For the $l$th target user (denoted as U$_l$), the SINR at the BS can be expressed as
\begin{equation}
    \gamma_l = \frac{P \left| \sqrt{\eta_l} g_l + \sum_{i=1}^{R} \sqrt{\eta_{li}} v_i h_i \Phi_l h_{li} \right|^2}
    {P \left| \sum_{m=1}^{U_I} \sqrt{\eta_m} g_m + \sum_{i=1}^{R} \sqrt{\eta_{mi}} v_i h_i \Phi_m h_{mi} \right|^2 + \sigma^2},
\end{equation}
where the numerator represents the effective signal power of user U$_l$ (e.g., body temperature, blood oxygen saturation, heart rate) received at the BS, and the denominator consists of AWGN noise and interference signals introduced via RIS reflections from interfering users (e.g., other runners or spectators with smart devices).

Our goal is to ensure reliable transmission of the physiological data from the target user while optimizing the RIS states and reflection coefficients to maximize the system SINR. This helps enhance the overall data transmission reliability and guarantees the user's health safety.

Therefore, we introduce a binary activation control vector for the RISs, denoted as $\mathbf{V} = \{v_1, v_2, ..., v_R\}$, where $v_i \in \{0,1\}$ indicates the ON/OFF state of RIS$_i$. Meanwhile, the phase shift of each reflection unit is adjusted by the RIS controller. Based on this, the system optimization problem can be formulated as the following joint optimization:

\begin{equation}
\begin{aligned}
P_1:\quad & \max_{\mathbf{V}, \mathbf{\Phi}}\ \gamma_l \\
& \text{s.t. } v_i \in \{0,1\}, && \forall i \in \mathcal{R},\\
& |e^{j\theta_n^i}| = 1, && \forall n \in [1, N],\ \forall i \in \mathcal{R}.
\end{aligned}
\end{equation}

This problem belongs to the class of mixed-integer nonlinear programming (MINLP). The objective or constraints contain at least one nonlinear term, and the optimization variables include both discrete variables (such as $v_i$) and continuous variables (such as $\Phi$), resulting in a typical hybrid discrete-continuous decision space.

To solve this problem efficiently and adapt to dynamic scenarios where the user's location changes continuously during marathon events, we propose a trajectory-based predictive control (TPC) algorithm. The algorithm is introduced in detail in the next section.


\section{Proposed TPC Algorithm}\label{s3}
This section introduces a trajectory prediction method based on the long short-term memory (LSTM) network, aiming to assist the base station (BS) in accurately inferring the future positions of users. Subsequently, we propose a reconfigurable intelligent surface (RIS) control algorithm that incorporates trajectory prediction, as illustrated in Fig.~\ref{fig:algorithm}. The algorithm relies on a well-trained LSTM model deployed at the BS to achieve near real-time prediction of user locations. Based on the predicted positions, the system can determine the optimal ON-OFF switching sequence and phase configuration of RISs in advance. In particular, we consider user-specific features such as movement speed and heading angle to further enhance prediction accuracy, thereby effectively mitigating interference and improving communication performance.

\subsection{LSTM-based Trajectory Prediction Model}
Trajectory prediction aims to infer a user's future location based on historical trajectory data. In this paper, for scenarios such as large-scale marathons, accurate prediction of users' future positions enables not only the optimization of the ON-OFF status of reconfigurable intelligent surfaces (RISs) but also improves interference suppression and uplink reliability. Due to its outstanding performance in modeling time sequences, the long short-term memory (LSTM) network is adopted for trajectory prediction tasks.

\subsubsection{Trajectory Data Collection}
According to 3GPP Release 18 \cite{R18} and Release 19 \cite{R19}, base stations can obtain and manage user location information, supporting high-accuracy positioning with errors typically within 50 meters. With the anticipated deployment of 6G networks, intelligent models are expected to be embedded at the BS, enabling online location prediction and reducing signaling overhead compared to traditional methods.

In this study, we use real-world user trajectory data collected in Beijing, leveraging the comprehensive GeoLife trajectory dataset \cite{geolife} which provides rich spatiotemporal information for collaborative social networking services. The dataset includes user longitude and latitude over time. This model can predict both the future position of the primary user and potential interfering users, providing a basis for subsequent RIS configuration.

\subsubsection{Offline Training Process}
The trajectory prediction model is trained using the collected dataset. In the training phase, the time-series data is processed using a sliding window approach. Each input contains 5 consecutive time steps of trajectory data from 64 independent trajectories. Each time step includes four features: longitude, latitude, speed, and heading angle (angle), which together form the input matrix of the model.

The model architecture consists of two LSTM layers to capture long-term dependencies in the time sequence. After LSTM encoding, a fully connected layer is applied to map the hidden state to the predicted coordinates at the next moment. This structure includes a memory update mechanism that adjusts its output based on current inputs and historical states, thus improving temporal modeling capabilities.

The model is trained by minimizing the mean squared error (MSE) using the Adam optimizer. The training is implemented using TensorFlow on a hardware platform with AMD Ryzen 7 7700 CPU and NVIDIA GeForce RTX 4060 Ti GPU.

\subsubsection{Online Inference Process}
Once trained, the model is deployed at the BS for online inference. During real-time operation, the BS continuously receives a user's historical trajectory and calculates the predicted coordinates at a future time step. The hidden state generated by the LSTM encoder is passed through a fully connected layer to obtain the predicted output. The non-linear mapping capability allows accurate and real-time trajectory forecasting.

This layered processing captures both the spatial correlation between features and temporal dependencies. The use of heading and speed information significantly enhances prediction robustness, especially in dynamic and complex environments.

\subsubsection{Error Analysis}
During the operation of the trajectory-based RIS control (TPC) strategy, the depth and accuracy of the LSTM prediction model directly affect the quality of RIS switching strategies. Accurate trajectory prediction improves the match between user position and RIS association, thereby improving signal strength and SINR. Position prediction errors can lead to suboptimal switching decisions and system performance degradation. Therefore, analyzing prediction errors and their impact on communication quality is critical for designing effective TPC strategies.The detailed error propagation analysis is as follows:
In trajectory-prediction-assisted RIS activation, the predicted location and angle of each user directly affect the estimation of path loss and thus influence the received signal and interference power. To analyze the error introduced by prediction in SINR, we derive the following model.

The SINR of a target user can be expressed as:
\begin{equation}
\text{SINR} = \frac{P_{\text{sig}}}{P_{\text{int}} + P_{\text{noise}}},
\end{equation}
where $P_{\text{sig}}$ and $P_{\text{int}}$ denote the received signal and interference power, respectively, and $P_{\text{noise}}$ is the noise power.

Assuming a path loss model, the signal and interference powers are given as:
\begin{equation}
P_{\text{sig}} = \frac{A}{d_{UB}^{\alpha}}, \quad P_{\text{int}} = \frac{B}{(d_{UR} \cdot d_{RB})^{\alpha}},
\end{equation}
where $d_{UB}$ is the distance from the user to the BS, $d_{UR}$ is the distance from the user to the RIS, and $d_{RB}$ is the distance from the RIS to the BS. $\alpha$ is the path loss exponent, and $A$, $B$ are gain constants.

The total received power is:
\begin{equation}
P_{\text{total}} = P_{\text{sig}} + P_{\text{int}}.
\end{equation}

Assume the predicted user location incurs a small disturbance, such that:
\begin{equation}
d_{UB}' = d_{UB} + \Delta d_1,\quad d_{UR}' = d_{UR} + \Delta d_2.
\end{equation}

Then, the predicted total power becomes:
\begin{equation}
P_{\text{total}}' = \frac{A}{(d_{UB} + \Delta d_1)^{\alpha}} + \frac{B}{[(d_{UR} + \Delta d_2)\cdot d_{RB}]^{\alpha}}.
\end{equation}

The change in received power due to prediction error is:
\begin{equation}
\begin{aligned}
\Delta P_{\text{total}} &= P_{\text{total}}' - P_{\text{total}} \\
&= A \left[ \frac{1}{(d_{UB} + \Delta d_1)^{\alpha}} - \frac{1}{d_{UB}^{\alpha}} \right] \\
&\quad + B \left[ \frac{1}{[(d_{UR} + \Delta d_2)\cdot d_{RB}]^{\alpha}} - \frac{1}{(d_{UR}\cdot d_{RB})^{\alpha}} \right].
\end{aligned}
\end{equation}

Meanwhile, the interference angle is modeled by a function:
\begin{equation}
f(\theta) = \frac{10}{\theta},
\end{equation}
where $\theta$ is the estimated angular separation between the target user and interfering users. If the prediction error causes an angular deviation $\Delta\theta$, then:
\begin{equation}
\Delta f = f(\theta + \Delta \theta) - f(\theta) = \frac{10}{\theta + \Delta \theta} - \frac{10}{\theta}.
\end{equation}

Assuming a linear dependence of interference power on the angular factor, the interference power error is:
\begin{equation}
\Delta P_{\text{int}} = K \cdot \Delta f,
\end{equation}
where $K$ is a proportionality constant related to the interfering user's transmission power and angle-based impact.

Finally, the SINR prediction error due to trajectory and angle estimation error can be approximated as:
\begin{equation}
\Delta \text{SINR} = \frac{P_{\text{total}}'}{P_{\text{int}}' + P_{\text{noise}}} - \frac{P_{\text{total}}}{P_{\text{int}} + P_{\text{noise}}},
\end{equation}
which captures the compound influence of spatial and angular prediction errors on system performance.


\subsection{Model Enhancement: Feature Augmentation for Dynamic Behavior}

In the original trajectory prediction model, user locations are typically described by geographic coordinates (latitude and longitude), which omit dynamic motion trends in temporal and spatial dimensions. This limitation restricts the model’s ability to handle fast-moving or abrupt user behavior. To address this issue, we enhance the LSTM-based trajectory prediction model by incorporating two physical features — \textbf{speed} and \textbf{movement angle} — on top of the original location data.

This enhancement provides a more comprehensive understanding of the user's motion state.

\subsubsection{Motivation and Background}

In scenarios such as long-distance sports like marathons, user movement often exhibits strong continuity and inertia. The current speed and heading at one time step significantly impact the subsequent trajectory. Relying solely on location may fail to capture turning, acceleration, or deceleration behaviors. This is especially problematic when users make sharp turns or experience sudden changes in speed.

Prediction error rises significantly in such scenarios. Therefore, it is necessary to introduce the physical quantities describing ``motion trends'' to improve the model's stability and accuracy.

\subsubsection{Extraction of Speed and Angle Features}

Given a trajectory sequence:
\[
\{(lat_1, lon_1), (lat_2, lon_2), \dots, (lat_T, lon_T)\}
\]

We estimate the speed feature $v_t$ by computing the geodesic distance between adjacent location points divided by the time interval $\Delta t$:
\begin{equation}
    v_t = \frac{\text{distance}(P_t, P_{t-1})}{\Delta t}
\end{equation}

Here, the \texttt{distance} function is computed using the Haversine formula to measure the great-circle distance on a sphere, and $\Delta t$ is the time interval between two consecutive points.

The movement angle $\theta_t$ is calculated using the bearing formula from geographic coordinates:
\begin{equation}
\begin{aligned}
\theta_t = \arctan2\big(
    &\sin(\Delta\lambda) \cdot \cos(lat_t), \\
    &\cos(lat_{t-1}) \cdot \sin(lat_t)\\
    &- \sin(lat_{t-1}) \cdot \cos(lat_t) \cdot \cos(\Delta\lambda)
\big)
\end{aligned}
\end{equation}


where $\Delta\lambda = lon_t - lon_{t-1}$.

Thus, each time step input becomes a 4-dimensional vector:
\begin{equation}
x_t = [lat_t, lon_t, v_t, \theta_t]
\end{equation}

\subsubsection{Consistent Model Structure and Training Approach}

The above four-dimensional features are uniformly fed into the original two-layer LSTM network without altering the network architecture, only expanding the input feature dimension. The training process still adopts mean squared error (MSE) as the loss function, aiming to minimize the spatial distance error between predicted and ground truth points. After introducing the new features, the model demonstrates faster convergence, smoother prediction results, and improved sensitivity to trajectory variations.

\subsubsection{Improvements and Significance}

Experimental results show that incorporating speed and angle features leads to a reduction in average prediction error, particularly in regions with sharp turns or significant speed changes. Furthermore, this enhancement improves the accuracy of subsequent RIS switch control, enabling the system to proactively adjust the reflection surface states in anticipation of sudden trajectory shifts. This method is particularly suitable for dynamic mobile scenarios with predictable trajectories, such as marathons, providing more reliable spatiotemporal priors for downstream communication strategies.



\section{RIS ON-OFF Control Mechanism}

After the user initiates an access request, the base station utilizes the trained LSTM model to predict the user's future trajectory. Based on the predicted position, the base station executes the RIS control algorithm to determine the binary ON or OFF state for each RIS, as illustrated in Fig.~\ref{fig:modelDesign}.
While the proposed TPC algorithm efficiently leverages trajectory prediction to enable proactive RIS ON-OFF control, the joint optimization of RIS states and phase configurations still presents a challenging mixed-integer non-convex problem. This section introduces a novel diffusion model-based optimization framework to address the combinatorial nature of RIS control under dynamic user trajectories and interference conditions.

\subsection{Motivation}

The optimization problem in Eq. (6) involves both binary ON-OFF control variables $v_i \in \{0, 1\}$ and discretized phase shifts $\theta_n^i \in \mathcal{Q}$, where $\mathcal{Q}$ denotes the set of quantized phase values determined by a $b$-bit resolution. Traditional optimization methods, including reinforcement learning or heuristic search, suffer from poor convergence, local optima, and limited adaptability in highly dynamic environments. Diffusion models, originally developed for image generation tasks, have recently demonstrated powerful capabilities in learning complex data distributions over high-dimensional spaces. By formulating the RIS configuration task as a conditional generative problem, we employ a denoising diffusion probabilistic model (DDPM) to sample high-quality RIS control vectors conditioned on predicted on-off and phase shifts.

\subsection{Diffusion Process Formulation}

Let the RIS control vector be $\mathbf{z} = [\mathbf{v}, \boldsymbol{\theta}] \in \mathbb{R}^D$, where $\mathbf{v}$ is the binary ON-OFF vector (relaxed to $[0, 1]^R$ during generation) and $\boldsymbol{\theta}$ denotes the phase vector aggregated from all RIS elements. The forward diffusion process corrupts the optimal configuration $\mathbf{z}_0$ by progressively adding Gaussian noise over $T$ steps:

\begin{equation}
    q(\mathbf{z}_t | \mathbf{z}_{t-1}) = \mathcal{N}(\mathbf{z}_t; \sqrt{1 - \beta_t} \mathbf{z}_{t-1}, \beta_t \mathbf{I}),
\end{equation}

where $\{\beta_t\}_{t=1}^T$ is a predefined variance schedule.

\subsection{Reverse Sampling with Conditional Denoising}

To recover the optimal configuration, we train a neural network $\epsilon_\theta(\mathbf{z}_t, t, \text{cond})$ to predict the noise added at each step, conditioned on user trajectory and CSI. The denoising process reconstructs the clean configuration via:

\begin{equation}
    \hat{\mathbf{z}}_{t-1} = \frac{1}{\sqrt{1 - \beta_t}} \left( \mathbf{z}_t - \frac{\beta_t}{\sqrt{1 - \bar{\alpha}_t}} \epsilon_\theta(\mathbf{z}_t, t, \text{cond}) \right),
\end{equation}

where $\bar{\alpha}_t = \prod_{s=1}^t (1 - \beta_s)$.

The condition input includes:
\begin{itemize}
    \item Predicted trajectory coordinates $\hat{\mathbf{x}}_u$ of all users;
    \item Channel coefficients $h_{li}, h_{mi}, g_l, g_m$ inferred during pilot signaling;
    \item Environmental metadata (optional), e.g., RIS-user distances.
\end{itemize}

\subsection{Discretization and Post-Processing}

At denoising step $t=0$, we apply the following post-processing steps:
\begin{enumerate}
    \item \textbf{RIS Activation}: Each $v_i$ is binarized by thresholding: $v_i = \mathbb{I}(v_i > 0.5)$;
    \item \textbf{Phase Quantization}: Each $\theta_n^i$ is projected to the nearest codeword in the $b$-bit discrete phase set $\mathcal{Q}$:
    \begin{equation}
        \mathcal{Q} = \left\{ 0, \frac{2\pi}{2^b}, \dots, \frac{2\pi(2^b - 1)}{2^b} \right\}.
    \end{equation}
\end{enumerate}

This ensures the generated configuration satisfies hardware constraints and conforms to the system model described in Section II.

\subsection{Inference Workflow}

During online inference, the trained DDPM is executed as follows:

\begin{algorithm}[H]
\caption{Diffusion-Based RIS Control Inference}
\begin{algorithmic}[1]
\State \textbf{Input:} Predicted trajectory $\hat{\mathbf{x}}_u$, channel state information
\State \textbf{Initialize:} Sample $\mathbf{z}_T \sim \mathcal{N}(0, I)$
\For{$t = T$ $1$}
    \State Predict noise $\epsilon_\theta(\mathbf{z}_t, t, \text{cond})$
    \State Update $\mathbf{z}_{t-1}$ via reverse denoising
\EndFor
\State Apply binarization and quantization to obtain $(\mathbf{v}^*, \boldsymbol{\Phi}^*)$
\State \textbf{Return:} RIS control decision
\end{algorithmic}
\end{algorithm}

\subsection{Advantages and Integration}

The proposed diffusion-based approach brings the following benefits:
\begin{itemize}
    \item \textbf{Robustness}: Effective under dynamic mobility and trajectory uncertainty;
    \item \textbf{Generality}: Supports discrete phase quantization and binary control in a unified framework;
    \item \textbf{Sample Efficiency}: Learns directly from optimal RIS configurations generated offline via supervised learning.
\end{itemize}

Combined with the LSTM-based trajectory prediction, this module completes an end-to-end intelligent RIS control pipeline suitable for real-time mobile health monitoring and ultra-reliable uplink communication.
\subsubsection{Phase Configuration Based on Codebook}

To calculate the phase shift of the RIS, the channel state information (CSI) of the $i$th RIS is denoted as $h_i$, $h_{li}$, $h_{mi}$, $g_l$, and $g_m$. Here, $U_l$ represents the target user, and the predicted coordinates are obtained from the base station. Based on the predicted position, the SINR of the signal received from $U_l$ can be calculated, as defined in Eq.~(5).

Assuming all RISs are turned on (i.e., $v_i = 1,\ \forall i \in \mathcal{R}$), the original problem $P_1$ can be simplified as the following optimization:

\begin{equation}
P_2:\quad \max_{\mathbf{\Phi}}\ \gamma_l
\end{equation}
\begin{equation*}
\text{s.t. } v_i = 1,\ \forall i \in \mathcal{R}, \quad |e^{j\theta_i^n}| = 1,\ \forall n \in [1,N],\ \forall i \in \mathcal{R}.
\end{equation*}

The phase configuration adopts the predefined codebook method~\cite{codebook1, codebook2}, with recent advances in coded beam training for RIS-assisted wireless communications providing enhanced efficiency \cite{codebook}, where the codebook is defined as:
\begin{equation}
\mathbf{\Phi} = \{\Phi_1, \Phi_2, ..., \Phi_R\}
\end{equation}

where:
\begin{equation}
\Phi_i = \mathrm{diag}(e^{j\theta_i^1}, e^{j\theta_i^2}, ..., e^{j\theta_i^N})
\end{equation}

If the quantization resolution is $b$ bits, then the phase set is given by:
\begin{equation}
\theta_i^n \in \left\{0, \frac{2\pi}{2^b}, ..., \frac{2\pi(2^b-1)}{2^b}\right\}
\end{equation}

For RIS $i$, the optimal phase configuration is obtained via:
\begin{equation}
\Phi_i^* = \arg \max_{\Phi \in \mathcal{P}} \Gamma_i
\end{equation}

where the signal gain function $\Gamma_i$ is defined as:
\begin{equation}
\Gamma_i = |\sqrt{\eta_l} g_l + \sqrt{\eta_{li}} h_i \Phi h_{li}|^2
\end{equation}

Here, $h_{li}$ and $h_i$ represent the channel gains of $U_l \rightarrow \mathrm{RIS}_i$ and $\mathrm{RIS}_i \rightarrow \mathrm{BS}$, respectively.

For $b = 2$ bits, each reflection unit can generate $2^N$ possible configurations. Each configuration lies in an $N$-dimensional continuous phase space, and the total configuration space increases exponentially with the number of RIS elements.

Once the optimal phase matrix $\Phi^*$ is obtained, the system can further optimize the binary ON-OFF control vector $\mathbf{V}^*$ to maximize overall system performance.

\subsubsection{RIS Binary Control Algorithm}

When $\text{RIS}_i$ is in the ON state ($\nu_i = 1$), the signal sent by user $\text{U}_u$ can be reflected by $\text{RIS}_i$, and the received SINR expression is shown in formula (5). If all RISs are in the OFF state ($\nu_i = 0$), only the direct link exists, and the received SINR is:

\begin{equation}
	\gamma_l'=\frac{P|\sqrt{\eta_l}g_l|^2}{\sum_{m=1}^{U_I}P|\sqrt{\eta_{m}}g_m|^2+\sigma^2}.
\label{gammaD}
\end{equation}

where, $U_I$ represents the number of interfering users, and $\sigma^2$ is the noise power.

To improve the system performance, when the predicted SINR satisfies $\gamma_l \geq \gamma_l'$, $\text{RIS}_i$ should be set to "ON"; conversely, when the incident angles of the target signal and the interference signal are too close, to avoid interference, $\text{RIS}_i$ should be turned OFF. This control strategy is described in detail in Algorithm 1.


\begin{algorithm}[thb]
\caption{RIS ON-OFF Control}\label{alg1}
\renewcommand{\algorithmicrequire}{\textbf{Input:}}
\renewcommand{\algorithmicensure}{\textbf{Output:}}
\begin{algorithmic}[1]
\Require Predicted coordinates of users, codebook $\mathcal{C}$
\Ensure Optimal ON-OFF  vector ${\mathbf V^*}\!=\! \{v_1^*,v_2^*,\ldots,v_R^*\}$
\State Initialize: $v_i = 1, \forall i \in \mathcal{R}$
\For{$i=1, i\leq R$}
    \State Calculate $\gamma_l$ and $\gamma_l'$ via (\ref{eq5}) and (\ref{gammaD}), respectively;
    \State Calculate $\Phi_i^*$ by solving problem $P_2$.
    \If{$\gamma_l$ \textless $\gamma_l'$}
        \State $v_i^*=0$, i.e., turn the RIS$_i$ OFF.
    \Else
         \State $v_i^*=1$, i.e., keep the RIS$_i$ ON.
    \EndIf
    \State  $ i\leftarrow i+1$;
    \State Update $v_i^*$ in $\mathbf{V^*}$.
\EndFor
\end{algorithmic}
\end{algorithm}

\subsubsection{Frame structure design}
In the TPC (Trajectory Prediction and Control) algorithm that introduces trajectory prediction and RIS control mechanism, the communication structure of each frame is divided into three stages, corresponding to the entire process of channel acquisition, RIS configuration, and data signal transmission respectively:
\begin{itemize}
\item {Stage 1 - pilot period}: In this stage, users simultaneously send channel estimation pilot signals and position information reference signals. The former is used for the base station to obtain the instantaneous channel state information (CSI) between the user and the base station and the RIS, and the latter is used to extract the user's spatial position features (such as time of arrival, longitude, and latitude, etc.). These position information will be used as the input for trajectory reconstruction and prediction, providing a priori basis for subsequent RIS control.
\item {Stage 2 - configuration period}: The base station uses the received historical position sequence as input, predicts the trajectory coordinates of the user at future moments through the trained LSTM model, and infers the potential interference area. On this basis, referring to the control process in Algorithm 1, the base station calculates the expected signal-to-noise ratio (SINR) of each RIS according to the predicted position, so as to judge its ON state, and selects the optimal phase matrix configuration from the predefined codebook. Specifically, if $\gamma_l \geq \gamma_l'$ is satisfied, the corresponding RIS will be turned ON ($\nu_i = 1$), and the optimal phase configuration $\Phi_i^*$ will be adopted; otherwise, it will be turned OFF to suppress potential interference.
\item {Stage 3 - transmission period}:In this stage, the user performs data transmission according to the configured RIS state and phase. The activated RIS will enhance the reflection gain of the user signal, while the deactivated RIS avoids the enhancement of interference signals from approximately the same direction, thereby effectively improving the overall link quality and system capacity.
\end{itemize}

\section{Error Analysis}

\section{Simulation Results} \label{s4}
To comprehensively evaluate the performance of the proposed trajectory prediction model, this study adopts the real - collected Geolife Trajectories dataset as the experimental basis and selects multiple representative user trajectory files as test samples. The designed trajectory prediction framework is based on the Long - Short - Term Memory network (LSTM). Its input is the historical trajectory points (including longitude, latitude, speed, and angle, etc.) of the user at consecutive time steps, and the output is the corresponding predicted results of future trajectory coordinates.

In the model training stage, the system selects a total of 71 trajectories from the user trajectory data collected in the actual environment of Beijing for training and testing. A sliding window method is used to construct the model input sequence, and each input is a sequence containing 5 consecutive trajectory points to enhance the generalization ability of the model. The model structure uses a two - layer LSTM network, which realizes the effective modeling of time - series features by retaining key historical information and introducing new states. In terms of the loss function, this study takes the mean squared error as the optimization objective.

In the model inference stage, after the system receives the user trajectory data, the trained LSTM model first predicts the trajectory points at future moments. To be closer to the requirements for position accuracy in wireless communication standards, we use the Haversine formula to calculate the geographical error between the predicted coordinates and the real coordinates, and take the average error in "meters" as the main evaluation index, thus quantifying the spatial accuracy of trajectory prediction.

The trajectory prediction results are also used for the RIS switch control inference at the base station side. By predicting the positions of users and interfering users in advance, combined with the RIS switch state algorithm and the phase configuration optimization scheme, more efficient communication resource scheduling and intelligent reflecting surface management are realized.

\subsection{Trajectory Prediction}

To verify the effectiveness of the trajectory prediction model in actual competition scenarios, we selected a representative athlete's movement trajectory as a test sample and used the trained LSTM model to predict its future movement path. As can be seen from Fig.~\ref{fig:result3}, the blue solid line represents the real trajectory of the athlete, and the orange dashed line is the model prediction result. Through visual analysis, it can be seen that the overall predicted trajectory of the model highly matches the real trajectory.

At key turning nodes, long - distance straight segments, and trajectory mutation regions, the predicted curve can still accurately track the athlete's forward direction, showing good spatiotemporal modeling ability. The experimental results show that the prediction errors of most test trajectories are stably controlled within the range of ±60 meters.

This result fully illustrates that the designed LSTM model can not only accurately learn the overall evolution trend of the athlete's movement trajectory but also has a good adaptability to local regions with drastic changes. In large - scale events similar to marathons, this model can be used as a core module to provide high - quality trajectory data support for task scheduling in subsequent intelligent communication systems, especially playing a core role in key tasks such as dynamic RIS (Reconfigurable Intelligent Surface) switch control, resource coordination, and interference avoidance.




\subsection{Performance Evaluation}
\subsubsection{Simulation settings}
Unless otherwise specified, the simulation model adopted in this section includes one base station (BS), $10$ reconfigurable intelligent surfaces (RISs), $1$ target user (i.e., the participating runner), and $10$ interfering users (such as communication interferences generated by other nearby runners or spectators). Each RIS contains $600$ reflecting elements and is evenly distributed on a circle with a radius of $10$ meters centered at the BS. We assume that the reflection power of the RIS is inversely proportional to the incident angle, that is, the reflection efficiency is the highest when the signal is incident vertically. In addition, the transmission power of the target user is set to $1$ watt, and the noise power is $1$ picowatt.


 \begin{figure}
    \centering
    \includegraphics[width=1.02\columnwidth]{true_vs_predict.png}
    \caption{Comparison of real trajectory and predicted trajectory}
    \label{fig:result3}
\end{figure}


\begin{figure}
    \centering
    \includegraphics[width=1.1\columnwidth]{dif_P_transmit.png}
    \caption{SINR comparison across transmission power}
    \label{fig:result1}
\end{figure}

\subsubsection{Simulation results}

\begin{figure}
    \centering
    \includegraphics[width=1.1\columnwidth]{dif_element.png}
    \caption{SINR comparison across numbers of RIS elements}
    \label{fig:result2}
\end{figure}

To clearly demonstrate the advantages of the proposed method, this study compares the proposed TPC (Trajectory Prediction Control) algorithm with two baseline methods under different transmission powers and RISs with different numbers of elements: one is the "RIS Always On" mode; the second is the ISL (Intelligent Spectrum Learning) control method based on reference \cite{tvt}.

In the competition scenario, we first select a historical trajectory of a real runner from the Geolife trajectory dataset, use the LSTM model to predict its movement path within the next $50$ seconds, and obtain the predicted trajectories of $10$ interfering users at the same time. Then, based on the prediction results and combined with the communication model, calculate and compare the signal - to - interference - plus - noise ratio (SINR) of the target user, and examine its change under different transmission powers and RIS quantity conditions.

As shown in Fig.~\ref{fig:result1}, with the increase of transmission power, the SINR of the three methods all increases. However, the proposed TPC method can predict the future position of the runner in advance, can judge the interference source and dynamically control the on - off states of each RIS, realize real - time interference avoidance, and thus obtain the highest SINR. In contrast, the ISL control method fails to predict future interference and only responds based on the current state, making it difficult to adapt to the dynamic changes of the trajectory scenario. And the "RIS Always On" scheme fails to identify the interference source and instead amplifies the interference signal in strong interference areas, performing the worst. Especially in the competition environment with dense interferences, blind reflection may lead to the interference power exceeding the desired signal, seriously affecting the communication quality.

Similarly, Fig.~\ref{fig:result2}shows the SINR change trend under different RIS element number configurations. Overall, as the number of RIS units increases, the signal enhancement effect improves and the SINR shows an upward trend. However, we also observe that when the number of RIS units is large, irrational reflections will further amplify the interference signal, causing serious spectrum pollution. And the proposed TPC algorithm effectively avoids the interference enhancement caused by invalid reflections through a reasonable ON - OFF control strategy, and finally achieves the optimal SINR performance.

\subsection{Error Analysis}

\subsection{Improving Trajectory Prediction Method}

\section{Conclusion }\label{s5}
To address the need for ensuring the life safety of runners in large - scale mobile scenarios such as marathon events, this paper proposes an intelligent reflecting surface (RIS) interference suppression algorithm based on trajectory prediction. This algorithm constructs a long - short - term memory network (LSTM) to predict the movement trajectory of runners and plans the on - off states of RIS in advance. Thus, it can effectively reduce communication interference, improve connection stability and data reliability in a dynamic uplink communication environment. Verified by simulation experiments, this algorithm has obvious advantages in the signal - to - interference - plus - noise ratio (SINR) index, providing a solid communication support for the real - time health monitoring and emergency response of runners.

Compared with traditional methods, the proposed TPC scheme not only has good interference suppression capability but also exhibits excellent predictability and adaptability in high - dynamic and high - density event environments. The integration of diffusion models in RIS optimization aligns with recent advances in conditional diffusion model-driven generative channels for double-RIS-aided wireless systems \cite{ConditionalDM2025}, opening new possibilities for intelligent wireless network design. In future work, we will further study the impact of trajectory prediction accuracy on the RIS control effect and explore high - precision trajectory perception and communication optimization methods under multi - source sensor data fusion to build a more complete and reliable safety guarantee system for runners.

\bibliographystyle{IEEEtran}
\bibliography{IEEEabrv,reference}


\end{document}
